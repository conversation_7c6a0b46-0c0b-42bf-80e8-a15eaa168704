/**
 * Custom caption enhancements for Vidstack default styles
 * Using vds-captions class from @vidstack/react/player/styles/default/captions.css
 */

/* Optional custom enhancements that don't conflict with Vidstack defaults */
:global(.vds-captions) {
  /* Enhanced font styling */
  --media-user-font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  --media-user-font-size: 1.1;

  /* Enhanced text shadow for better readability */
  --media-user-text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 4px rgba(0, 0, 0, 0.5);

  /* Enhanced background for better contrast */
  --media-cue-bg: rgba(0, 0, 0, 0.85);

  /* Enhanced border radius */
  --media-cue-border-radius: 6px;

  /* Enhanced backdrop filter */
  --media-cue-backdrop: blur(12px);

  /* Enhanced box shadow */
  --media-cue-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Debugging: Force captions to be visible temporarily */
:global(.vds-captions[data-example]) {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure captions are visible when enabled */
:global(.vds-captions:not([aria-hidden="true"])) {
  opacity: 1 !important;
  visibility: visible !important;
}
