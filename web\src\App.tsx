import { Routes, Route, Navigate, lazy, Suspense } from "react-router-dom";
import { AuthProvider, useAuth } from "@/lib/auth";
import { Skeleton } from "@/components/ui/skeleton";

const DashboardLayout = lazy(
  () => import("@/components/dashboard/DashboardLayout")
);
const VideoUpload = lazy(() => import("@/components/dashboard/VideoUpload"));
const VideoDetails = lazy(() => import("@/components/dashboard/VideoDetails"));
const VideoPlayer = lazy(() => import("@/components/dashboard/VideoPlayer"));
const MyVideos = lazy(() => import("@/components/dashboard/MyVideos"));
const DashboardAnalytics = lazy(
  () => import("@/components/dashboard/DashboardAnalytics")
);
const LandingPage = lazy(() => import("@/components/landing/LandingPage"));
const LoginForm = lazy(() => import("@/components/auth/LoginForm"));
const SignupForm = lazy(() => import("@/components/auth/SignupForm"));

const LoadingFallback = () => (
  <div className="min-h-screen bg-background flex items-center justify-center">
    <div className="space-y-4 w-full max-w-md">
      <Skeleton className="h-8 w-3/4 mx-auto" />
      <Skeleton className="h-4 w-1/2 mx-auto" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Public Route Component
const PublicRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

function App() {
  return (
    <AuthProvider>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginForm />
              </PublicRoute>
            }
          />
          <Route
            path="/signup"
            element={
              <PublicRoute>
                <SignupForm />
              </PublicRoute>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/dashboard/*"
            element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<DashboardAnalytics />} />
            <Route path="upload" element={<VideoUpload />} />
            <Route path="video/:videoId" element={<VideoDetails />} />
            <Route path="play/:videoId" element={<VideoPlayer />} />
            <Route path="videos" element={<MyVideos />} />
          </Route>

          {/* Catch all route - redirect to dashboard if logged in, otherwise to login */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Suspense>
    </AuthProvider>
  );
}

export default App;
