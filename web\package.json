{"name": "starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc ; vite build", "build-no-errors": "tsc ; vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "types:supabase": "npx supabase gen types typescript --project-id $SUPABASE_PROJECT_ID > src/types/supabase.ts"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.45.6", "@vidstack/player": "^0.13.3", "@vidstack/react": "^1.9.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.5", "framer-motion": "^11.18.0", "hls.js": "^1.5.20", "lucide-react": "^0.394.0", "media-captions": "^0.0.18", "media-icons": "^1.0.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-resizable-panels": "^2.0.19", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "recharts": "^2.15.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "vidstack": "^0.6.15", "zod": "^3.23.8"}, "devDependencies": {"@swc/core": "1.3.96", "@types/node": "^20.14.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react-swc": "^3.10.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "tailwindcss": "3.4.1", "tempo-devtools": "^2.0.94", "typescript": "^5.2.2", "vite": "^6.3.5"}}